locals {
  resource_name_suffix = "cosmosdbmongotest-01"
}

module "rg01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.resource_name_suffix
}


module "cosmosdb" {

  // Create Cosmos DB Account first
  // source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-cosmosdb//mongodb?ref=v1.3.0"
  source               = "../.."
  conventions          = module.conventions
  resource_group_name  = module.rg01.rgrp.name
  resource_name_suffix = local.resource_name_suffix
  subnet_id            = data.azurerm_subnet.sn_privateendpoint.id
  wait_after           = 300
  ip_range_filter      = ["*************", "************", "***********", "************", "*************"] //example IP list allows requests from the Azure portal 

  // Optional: Limit the total throughput providisoned on your Azure Cosmos DB Account
  //total_throughput_limit = 1000

  consistency_policy = {
    consistency_level       = "BoundedStaleness" // can be either BoundedStaleness, Eventual, Session, Strong or ConsistentPrefix
    max_interval_in_seconds = 300
    max_staleness_prefix    = 100000
  }

  /* Example for another consistency policy which does not require additional parameters
  consistency_policy = {
    consistency_level  = "Eventual" 
  }
*/

  // Example for a standalone deployment
  failover_locations = [
    {
      location          = "westeurope"
      failover_priority = 0
      zone_redundant    = false
    }
  ]

  /*
  backup = {
    type                = "Periodic"
    interval_in_minutes = 240
    retention_in_hours  = 8
  } 
*/

  //Diagnostic settings
  log_analytics_workspace_id = module.conventions.log_analytics_workspace_id
  //Example with all metric types:
  log_analytics_metrics = ["AllMetrics"]
  //Example with list of log types:
  //log_analytics_diag_logs    = ["MongoRequests"]
  //Example with all log types:
  log_analytics_diag_logs = ["AllLogs"]


}


