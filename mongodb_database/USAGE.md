<!-- BEGIN_TF_DOCS -->
## Name of the module
Azure Cosmos DB MongoDB Database

shortname: -

terraform resource: azurerm\_cosmosdb\_mongo\_database, azurerm\_cosmosdb\_mongo\_collection

## Short description of the module
This Terraform module deploys an Azure Cosmos DB MongoDB database and collection.

## Detailed description on Confluence
[Azure Cosmos DB ](https://confluence.otpbank.hu/x/7YN6Kw)

## Release notes – changes in the current and previous versions
[CHANGELOG.md](../CHANGELOG.md)

## Terraform version compatibility
Terraform >= 1.7.4

## Necessary Terraform providers, and compatibility to provider versions
provider registry.terraform.io/hashicorp/azurerm >= 4.2.0

## Resources generated by the module
- MongoDB database and collection

## Requirements

The following requirements are needed by this module:

- <a name="requirement_azurerm"></a> [azurerm](#requirement\_azurerm) (>= 4.2.0)

## Providers

The following providers are used by this module:

- <a name="provider_azurerm"></a> [azurerm](#provider\_azurerm) (>= 4.2.0)


## Example for Provider configuration

```hcl
provider "azurerm" {
  features {
    resource_group {
      prevent_deletion_if_contains_resources = false
    }
  }
  storage_use_azuread = true
}

provider "time" {
}

#required_providers - Make sure that you are using versions, which available on https://az-nexus.otpbank.hu/repository/nexus-terraform-provider/.
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.10.0"
    }
    time = {
      source  = "hashicorp/time"
      version = "0.11.2"
    }
  }
}

```

## Example for Convention

```hcl
module "conventions" {
  //Checkov  
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash      
  source      = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-base//conventions?ref=v2.0.3"
  cloud       = var.cloud
  environment = var.environment
  project     = var.project
  region      = var.region
  subsidiary  = var.subsidiary
  tags = {
    "OwnerOU"      = "ccoe"
    "OwnerContact" = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    "Criticality"  = "Low"
  }
}

```

## Example for MongoDB Database creation

```hcl
locals {
  resource_name_suffix = "cosmosdbmongodb-02"
}

module "rg01" {
  //Checkov
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash  
  source               = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-rg?ref=v1.2.0"
  conventions          = module.conventions
  resource_name_suffix = local.resource_name_suffix
}

module "cosmosdb" {

  // Create Cosmos DB Account first

  // source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-cosmosdb//mongodb?ref=v1.3.0"
  source               = "../../../mongodb"
  conventions          = module.conventions
  resource_group_name  = module.rg01.rgrp.name
  resource_name_suffix = local.resource_name_suffix
  subnet_id            = data.azurerm_subnet.sn_privateendpoint.id
  wait_after           = 120
  ip_range_filter      = ["*************", "************", "***********", "************", "*************"] //example IP list allows requests from the Azure portal 

  // Optional: Limit the total throughput providisoned on your Azure Cosmos DB Account
  // total_throughput_limit = 1000

  consistency_policy = {
    consistency_level       = "BoundedStaleness" ## can be either BoundedStaleness, Eventual, Session, Strong or ConsistentPrefix
    max_interval_in_seconds = 300
    max_staleness_prefix    = 100000
  }

  // Example for a standalone deployment
  failover_locations = [
    {
      location          = "westeurope"
      failover_priority = 0
      zone_redundant    = false
    }
  ]

  backup = {
    type                = "Periodic"
    interval_in_minutes = 240
    retention_in_hours  = 8
  }

  //Diagnostic settings
  log_analytics_workspace_id = module.conventions.log_analytics_workspace_id
  //Example with all metric types:
  log_analytics_metrics = ["AllMetrics"]
  //Example with list of log types:
  //log_analytics_diag_logs    = ["MongoRequests"]
  //Example with all log types:
  log_analytics_diag_logs = ["AllLogs"]

}

module "mongodatabase" {

  // source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-azurerm-cosmosdb//mongodb_database?ref=v1.3.0"
  source              = "../.."
  conventions         = module.conventions
  resource_group_name = module.rg01.rgrp.name
  account_name        = module.cosmosdb.cmdb.name

  databases = {
    test_db01 = {
      // There are two ways of configuring throughput (RU/s); manual or autoscale.
      // To use manual throughput, set throughput either on a database or a collection
      // To use autoscale throughput, set max_throughput on a database or a collection.
      // Note: throughput and max_throughput cannot be used in conjunction
      throughput = 500
      collections = [
        { name = "col0", shard_key = "somekey_0" },
        { name = "col1", shard_key = "somekey_1" },
      ]
    }
    test_db02 = {
      max_throughput = 1000
      collections = [
        { name = "col2", shard_key = "someotherkey" },
        { name = "col3", shard_key = "someotherkey" },
      ]
    }
  }

}


```


## Resources

The following resources are used by this module:

- [azurerm_cosmosdb_mongo_collection.coll](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/cosmosdb_mongo_collection) (resource)
- [azurerm_cosmosdb_mongo_database.main](https://registry.terraform.io/providers/hashicorp/azurerm/latest/docs/resources/cosmosdb_mongo_database) (resource)

## Required Inputs

The following input variables are required:

### <a name="input_account_name"></a> [account\_name](#input\_account\_name)

Description: (Required) The name of the Cosmos DB Mongo Database to create the table within. Changing this forces a new resource to be created.

Type: `string`

### <a name="input_conventions"></a> [conventions](#input\_conventions)

Description: (Required) terraform-conventions module

Type: `any`

### <a name="input_resource_group_name"></a> [resource\_group\_name](#input\_resource\_group\_name)

Description: (Required) Specifies the name of the resource group.

Type: `string`

## Optional Inputs

The following input variables are optional (have default values):

### <a name="input_databases"></a> [databases](#input\_databases)

Description: List of databases  
  throughput - (Optional) The throughput of the MongoDB database (RU/s). Must be set in increments of 100. The minimum value is 400. This must be set upon database creation otherwise it cannot be updated without a manual terraform destroy-apply.  
  max\_throughput - (Optional) The maximum throughput of the MongoDB database (RU/s). Must be between 1,000 and 1,000,000. Must be set in increments of 1,000. Conflicts with throughput.  
  Collections:  
    name - (Required) Specifies the name of the Cosmos DB Mongo Collection. Changing this forces a new resource to be created.  
    shard\_key - (Optional) The name of the key to partition on for sharding. There must not be any other unique index keys. Changing this forces a new resource to be created.  
    throughput - (Optional) The throughput of the MongoDB collection (RU/s). Must be set in increments of 100. The minimum value is 400. This must be set upon database creation otherwise it cannot be updated without a manual terraform destroy-apply.  
    max\_throughput - (Optional) The maximum throughput of the MongoDB collection (RU/s). Must be between 1,000 and 1,000,000. Must be set in increments of 1,000. Conflicts with throughput.  
    index\_unique - (Optional) Is the index unique or not? Defaults to false.

Type:

```hcl
map(object({
    throughput     = optional(number)
    max_throughput = optional(number)
    collections = list(object({
      name           = string
      shard_key      = string
      throughput     = optional(number)
      max_throughput = optional(number)
      index_unique   = optional(bool)
    }))
  }))
```

Default: `{}`

## Outputs

The following outputs are exported:

### <a name="output_cmdb"></a> [cmdb](#output\_cmdb)

Description: MongoDB database

## Contributing

* If you think you've found a bug in the code or you have a question regarding
  the usage of this module, please reach out to <NAME_EMAIL>
* Contributions to this project are welcome: if you want to add a feature or a
  fix a bug, please do so by opening a Pull Request in this repository.
  In case of feature contribution, we kindly ask you to send a mail to
  discuss it beforehand.
<!-- END_TF_DOCS -->